<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader } from '@/components/ui/loader';
import { useDateFormats } from '@/composables/useDateFormats';
import { useLeaveForm } from '@/composables/useLeaveForm';
import { FormMode } from '@/enums';
import { LeaveOfType } from '@/enums/leave';
import { isEmployee } from '@/helpers/staff-helper';
import { cn } from '@/lib/utils';
import { IonPage } from '@ionic/vue';
import {
  NCheckbox,
  NDatePicker,
  NForm,
  NFormItem,
  NInput,
  NInputNumber,
  NSelect,
} from 'naive-ui';
import { computed, ref, watch } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const {
  form,
  formRef,
  hasEmployees,
  rules,
  relTypeOptions,
  leaveTypeOptions,
  staffOptions,
  staffManagerOptions,
  attendanceStatusOptions,
  remainDays,
  totalDaysOff,
  isSubmitDisabled,
  isLoading,
  RelTypeEnum,
  handleSubmit,
  user,
} = useLeaveForm(FormMode.CREATE);

const { dateFormats } = useDateFormats();

// Session options
const sessionOptions = [
  { label: t('leaves.new_request.full_day'), value: 'full_day' },
  { label: t('leaves.new_request.morning'), value: 'morning' },
  { label: t('leaves.new_request.afternoon'), value: 'afternoon' },
];

// Add session field to form
const session = ref('full_day');

// Computed properties for conditional rendering
const isLeaveType = computed(() => form.value.rel_type === RelTypeEnum.LEAVE);
const isNonEmployee = computed(() => !isEmployee(user?.value?.role.name || ''));
const shouldShowAttendanceStatus = computed(() => {
  const isEarlyOrLate =
    form.value.rel_type === RelTypeEnum.EARLY ||
    form.value.rel_type === RelTypeEnum.LATE;
  const isUnpaidLeave = form.value.type_of_leave === LeaveOfType.UNPAID_LEAVE;
  return isNonEmployee.value && (isEarlyOrLate || isUnpaidLeave);
});

// Function to set time based on session
const setTimeBySession = (date: number | null, sessionType: string) => {
  if (!date) return date;

  const dateObj = new Date(date);

  switch (sessionType) {
    case 'morning':
      // Morning: 8:00 AM to 12:00 PM
      if (form.value.start_time === date) {
        dateObj.setHours(8, 0, 0, 0);
      } else {
        dateObj.setHours(12, 0, 0, 0);
      }
      break;
    case 'afternoon':
      // Afternoon: 1:00 PM to 6:00 PM
      if (form.value.start_time === date) {
        dateObj.setHours(13, 0, 0, 0);
      } else {
        dateObj.setHours(18, 0, 0, 0);
      }
      break;
    case 'full_day':
      // Full day: 8:00 AM to 6:00 PM
      if (form.value.start_time === date) {
        dateObj.setHours(8, 0, 0, 0);
      } else {
        dateObj.setHours(18, 0, 0, 0);
      }
      break;
  }

  return dateObj.getTime();
};

// Watch for session changes and update times
watch(session, (newSession) => {
  if (form.value.start_time) {
    form.value.start_time = setTimeBySession(form.value.start_time, newSession);
  }
  if (form.value.end_time) {
    form.value.end_time = setTimeBySession(form.value.end_time, newSession);
  }
});

// Watch for start_time changes and auto-set end_time based on session
watch(
  () => form.value.start_time,
  (newStartTime) => {
    if (newStartTime && isLeaveType.value) {
      form.value.end_time = setTimeBySession(newStartTime, session.value);
    }
  },
);
</script>

<template>
  <ion-page>
    <div :class="cn(
      'scroll-container mb-7 flex h-full flex-col items-center gap-y-7 p-4',
      isLoading ? 'justify-center' : 'justify-start',
    )
      ">
      <Loader v-if="isLoading" />
      <n-form v-else ref="formRef" label-placement="top" :model="form" :rules="rules" class="w-full">
        <n-form-item :label="t('leaves.new_request.subject')" path="subject" required>
          <n-input v-model:value="form.subject" :placeholder="t('leaves.new_request.subject_placeholder')" />
        </n-form-item>

        <n-checkbox v-if="isNonEmployee" v-model:checked="hasEmployees" size="small"
          :class="cn('w-full', hasEmployees ? 'mb-2' : 'mb-4')">
          {{ t('leaves.new_request.register_for_employee') }}
        </n-checkbox>

        <n-form-item v-if="isNonEmployee && hasEmployees" :label="t('leaves.new_request.staff')" path="employee_ids">
          <n-select filterable multiple v-model:value="form.employee_ids" :options="staffOptions"
            :placeholder="t('leaves.new_request.staff_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.rel_type')" path="rel_type">
          <n-select v-model:value="form.rel_type" :options="relTypeOptions"
            :placeholder="t('leaves.new_request.rel_type_placeholder')" />
        </n-form-item>

        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.type_of_leave')" path="type_of_leave">
          <n-select v-model:value="form.type_of_leave" :options="leaveTypeOptions"
            :placeholder="t('leaves.new_request.type_of_leave_placeholder')" />
        </n-form-item>

        <!-- Session Selection for Leave Type -->
        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.session')" path="session">
          <n-select v-model:value="session" :options="sessionOptions"
            :placeholder="t('leaves.new_request.session_placeholder')" />
        </n-form-item>

        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.days')" path="number_of_leaving_day" required>
          <n-input-number v-model:value="form.number_of_leaving_day"
            :placeholder="t('leaves.new_request.days_placeholder')" :min="0" :step="session === 'full_day' ? 1 : 0.5"
            :default-value="session === 'full_day' ? 1 : 0.5" />
        </n-form-item>

        <div v-if="isLeaveType" class="mb-4 space-y-1 font-semibold">
          <p>
            {{ t('leaves.new_request.total_days_off') }}: {{ totalDaysOff }}
          </p>
          <p>{{ t('leaves.new_request.remaining_days') }}: {{ remainDays }}</p>
        </div>

        <n-form-item :label="t('leaves.new_request.start_date')" path="start_time" required>
          <n-date-picker v-model:value="form.start_time" :format="dateFormats.date.display"
            :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
        </n-form-item>

        <n-form-item v-if="isLeaveType" :label="t('leaves.new_request.end_date')" path="end_time" required>
          <n-date-picker v-model:value="form.end_time" :format="dateFormats.date.display"
            :placeholder="dateFormats.date.placeholder" clearable class="w-full" />
        </n-form-item>

        <!-- Time Display for Selected Session -->
        <div v-if="isLeaveType && session !== 'full_day'" class="mb-4 rounded bg-blue-50 p-3">
          <p class="text-sm text-blue-700">
            <span class="font-medium">{{ t('leaves.new_request.session') }}:</span>
            {{
              session === 'morning'
                ? t('leaves.new_request.morning') + ' (8:00 AM - 12:00 PM)'
                : t('leaves.new_request.afternoon') + ' (1:00 PM - 6:00 PM)'
            }}
          </p>
        </div>

        <n-form-item :label="t('leaves.new_request.follower')" path="follower_id">
          <n-select filterable v-model:value="form.follower_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.follower_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.approver')" path="approver_id">
          <n-select filterable v-model:value="form.approver_id" :options="staffManagerOptions"
            :placeholder="t('leaves.new_request.approver_placeholder')" />
        </n-form-item>

        <n-form-item v-if="shouldShowAttendanceStatus" :label="t('leaves.new_request.attendance_status')"
          path="is_deducted_attendance">
          <n-select v-model:value="form.is_deducted_attendance" :options="attendanceStatusOptions"
            :placeholder="t('leaves.new_request.attendance_status_placeholder')" />
        </n-form-item>

        <n-form-item :label="t('leaves.new_request.reason')" path="reason">
          <n-input v-model:value="form.reason" type="textarea"
            :placeholder="t('leaves.new_request.reason_placeholder')" />
        </n-form-item>

        <n-form-item v-if="form.type_of_leave == LeaveOfType.SICK_LEAVE" :label="t('common.choose_file')">
          <Input type="file" />
        </n-form-item>

        <Button type="submit" size="lg" class="w-full" @click="handleSubmit" :disabled="isSubmitDisabled">
          {{ t('leaves.new_request.submit') }}
        </Button>
      </n-form>
    </div>
  </ion-page>
</template>

<style scoped>
.n-form-item-label::after {
  content: '*';
  color: red;
  margin-left: 4px;
}

.n-date-picker {
  width: 100%;
}

.n-checkbox {
  --n-label-font-weight: 500 !important;
}
</style>
