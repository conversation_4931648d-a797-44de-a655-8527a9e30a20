<script setup lang="ts">
import { useLeaveRequests } from '@/composables/useLeaveRequests';
import { useLocale } from '@/composables/useLocale';
import { LIMIT } from '@/constants';
import {
  getRequestDisplayInfo,
  getStatusClass,
  getStatusText,
} from '@/constants/leave-history';
import { RelType } from '@/enums/leave';
import type { Leave } from '@/interfaces/leave';
import { formatDate } from '@/utils/format';
import { Paperclip } from 'lucide-vue-next';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Button } from '../ui/button';
import { getLocalizedPath } from '@/utils/localized-navigation';
import { useRouter } from 'vue-router';
import { Status } from '@/enums';


const router = useRouter();
const { t } = useI18n();
const { locale } = useLocale();
const { recentRequestQuery } = useLeaveRequests();
const canEdit = (request: Leave) => {
  return request.status === Status.PENDING;
};

const handleEdit = (request: Leave) => {
  const editPath = getLocalizedPath(`/leaves/${request.id}/edit`, locale.value);
  router.push(editPath);
};

const { data: recentRequestData, isLoading } = recentRequestQuery(LIMIT);
const recentRequests = computed(() => recentRequestData.value || []);
const hasRequests = computed(() => recentRequests.value.length > 0);

const formatTimeDisplay = (item: Leave) => {
  if (item.rel_type === RelType.LEAVE) {
    if (item.start_time === item.end_time) {
      return formatDate(item.start_time, locale.value);
    }
    return `${formatDate(item.start_time, locale.value)} - ${formatDate(item.end_time, locale.value)}`;
  } else {
    return (
      formatDate(item.start_time, locale.value) ||
      formatDate(item.datecreated, locale.value)
    );
  }
};

const formatDurationDisplay = (item: Leave) => {
  if (item.rel_type === RelType.LEAVE) {
    const days = item.number_of_leaving_day;
    return `${days} ${days === 1 ? t('leaves.common.count_day') : t('leaves.common.count_days')}`;
  } else {
    return t('leaves.common.not_applicable');
  }
};

const getDisplayTitle = (item: Leave) => {
  return getRequestDisplayInfo(item).title;
};
</script>

<template>
  <div class="flex w-full flex-col gap-4">
    <div class="text-lg font-semibold text-gray-800 capitalize">
      {{ t('leaves.recent_leave.title') }}
    </div>

    <!-- Loading Skeleton -->
    <div v-if="isLoading" class="flex flex-col gap-3">
      <div v-for="i in LIMIT" :key="`skeleton-${i}`" class="animate-pulse rounded-md border border-gray-200 p-4">
        <div class="flex items-center justify-between">
          <!-- Skeleton for request type -->
          <div class="h-4 w-32 rounded bg-gray-200"></div>
          <!-- Skeleton for status badge -->
          <div class="h-6 w-16 rounded-full bg-gray-200"></div>
        </div>
        <!-- Skeleton for subject -->
        <div class="mt-2 h-4 w-48 rounded bg-gray-200"></div>
        <!-- Skeleton for time/date -->
        <div class="mt-2 h-3 w-40 rounded bg-gray-200"></div>
        <!-- Skeleton for duration -->
        <div class="mt-1 h-3 w-24 rounded bg-gray-200"></div>
        <!-- Skeleton for reason (optional) -->
        <div class="mt-1 h-3 w-full rounded bg-gray-200"></div>
      </div>
    </div>

    <!-- No Requests State -->
    <div v-else-if="!hasRequests" class="flex items-center justify-center p-5">
      <p class="w-full text-center text-gray-500">
        {{ t('leaves.recent_leave.no_requests') }}
      </p>
    </div>

    <!-- Loaded Requests -->
    <div v-else class="flex flex-col gap-3">
      <div v-for="request in recentRequests" :key="request.id"
        class="rounded-md border border-gray-200 p-4 transition-shadow hover:shadow-sm">
        <div class="flex items-center justify-between">
          <div class="text-sm font-medium text-gray-700">
            {{ getDisplayTitle(request) }}
          </div>
          <div class="rounded-full px-2 py-1 text-xs font-medium" :class="getStatusClass(request.status)">
            {{ getStatusText(request.status) }}
          </div>
        </div>

        <!-- Subject display -->
        <div v-if="request.subject" class="mt-2 font-semibold text-gray-900">
          {{ request.subject }}
        </div>

        <!-- Time/Date display -->
        <div class="mt-2 text-sm text-gray-600">
          {{ formatTimeDisplay(request) }}
        </div>

        <!-- Duration display (only for leave requests) -->
        <div v-if="request.rel_type === RelType.LEAVE" class="mt-1 text-sm text-gray-500">
          {{ formatDurationDisplay(request) }}
        </div>
        <div class="flex justify-between items-center">
          <div v-if="request.reason" class="mt-2 line-clamp-2 text-sm text-gray-700">
            {{ request.reason }}
          </div>
          <div v-if="canEdit(request)">
            <Button size="sm" @click="() => handleEdit(request)">
              Edit
            </Button>
          </div>
        </div>
        <!-- Reason display -->

        <!-- Group info (if applicable) -->
        <div v-if="request.group_members && request.group_members.length > 0" class="mt-2 text-xs text-blue-600">
          {{
            t('leaves.common.group_request', {
              count: request.group_members.length,
            })
          }}
        </div>

        <!-- Attachment indicator -->
        <div v-if="request.attachment" class="mt-1 text-xs text-green-600">
          <Paperclip /> {{ t('leaves.common.has_attachment') }}
        </div>
      </div>
    </div>
  </div>
</template>
