import type { Notification } from '@/interfaces/common';

const getDate = (daysAgo: number, hoursAgo = 0, minutesAgo = 0): Date => {
  const date = new Date();
  date.setDate(date.getDate() - daysAgo);
  date.setHours(date.getHours() - hoursAgo);
  date.setMinutes(date.getMinutes() - minutesAgo);
  return date;
};

export const mockNotifications: Notification[] = [
  {
    id: 1,
    type: 'approve',
    title: 'New feature available',
    message:
      "We've just launched a new feature that helps you organize your tasks better.",
    time: getDate(0, 0, 15),
    read: false,
    sender: {
      name: 'System',
      avatar:
        'https://images.pexels.com/photos/3861969/pexels-photo-3861969.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    },
  },
  {
    id: 2,
    type: 'pending',
    title: 'Payment processed',
    message: 'Your payment of $24.99 has been successfully processed.',
    time: getDate(0, 1, 30),
    read: false,
    sender: {
      name: 'Billing Department',
      avatar:
        'https://images.pexels.com/photos/7681081/pexels-photo-7681081.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    },
    link: '#',
  },
  {
    id: 3,
    type: 'reject',
    title: 'Storage limit approaching',
    message:
      "You've used 85% of your storage quota. Consider upgrading your plan.",
    time: getDate(0, 3, 45),
    read: true,
    sender: {
      name: 'Storage Monitor',
      avatar:
        'https://images.pexels.com/photos/5380642/pexels-photo-5380642.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2',
    },
  },
];
