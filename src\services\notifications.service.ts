import type { Notification } from '@/interfaces/common';
import apiClient from '@/api/axios-instance';
import { mockNotifications } from '@/mocks/notifications';

export function fetchNotifications(): Promise<Notification[]> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(mockNotifications);
    }, 500);
  });
}

export const getNotifications = async (): Promise<Notification[]> => {
  const { data } = await apiClient.get<Notification[]>
  ('/notifications');
console.log(data)
  return data;
};
