import type { AttendanceType, ShiftType, WorkType } from "@/enums/attendaces";
import type { EmployeeInfo, ManagementInfo, StaffInfo } from "./staff";

import type { Action } from "@/enums/leave";
import type { ApiResponse } from "@/types";
import type { Status } from "@/enums";

export interface Attendance {
  id: number;
  date: string;
  type?: AttendanceType;
  time_in: string;
  time_out: string;
  total_time: string;
}

export interface WorkSchedule {
  date: Date;
  title: string;
  description: string;
  startTime: string | Date | number;
  endTime: string;
  status: 'present' | 'absent' | 'half-day' | 'on-leave';
}

export interface WorkShift {
  id: number;
  shift_code: string;
  time_start: string;
  time_end: string;
  description: string;
  segments: Segment[];
}

export interface Segment {
  start: string;
  end: string;
  description: string;
  shift_type: ShiftType;
  work_type: WorkType;
}

export interface Holiday {
  id: number;
  holiday_name: string;
  holiday_date: string;
  off_type: string;
  days_remaining: number;
  day_of_week: number;
  day_of_week_label: string;
}

export interface OvertimeRequest {
  employees: number[];
  creator?: number;
  additional_day: string;
  timekeeping_value: string;
  time_in: string;
  time_out: string;
  approver: number;
  reason: string;
}

export interface Overtime {
  id: number |string;
  additional_day: string;
  registration_type: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: Status;
  status_text: string;
  serious_number: number;
  reject_reason: string;
  creator: number;
  approver: number;
  creator_info: StaffInfo;
  approver_info: StaffInfo | null;
  can_edit?: boolean;
  group_info?: GroupOvertimeInfo & { employees: OvertimeEmployee[] };
  employees?: OvertimeEmployee[]; // For group overtime at root level
}

// Interface for overtime employee from API response
export interface OvertimeEmployee {
  id: number;
  employee_id: number;
  employee_status: number;
  employee_status_text: string;
  employee_info: {
    staff_id: number;
    full_name: string;
    staff_identifi: string;
    email: string;
  };
}

export interface GroupOvertimeInfo {
  group_code: string;
  registration_type: string;
  additional_day: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  session: number;
  creator: number;
  approver: number | null;
  creator_info: StaffInfo;
  approver_info: StaffInfo;
  can_approve: boolean;
  can_reject: boolean;
  created_at: string;
  can_edit: boolean;
}

export interface GroupOvertimeRequest {
  group_info: GroupOvertimeInfo & { total_employees: number };
  employees: EmployeeInfo[];
}

export interface IndividualOvertimeRequest {
  id: number;
  type: string;
  registration_type: string;
  additional_day: string;
  time_in: string;
  time_out: string;
  timekeeping_value: number;
  reason: string;
  status: number;
  status_text: string;
  reject_reason: string;
  creator: number;
  approver: number | null;
  employee_id: number;
  creator_info: StaffInfo;
  employee_info: StaffInfo;
  can_approve: boolean;
  can_reject: boolean;
  created_at: string;
}

export interface ManagementOvertimeResponse {
  success: boolean;
  data: (GroupOvertimeRequest | IndividualOvertimeRequest)[];
  pagination: Pagination;
  total: number;
  filters_applied: Record<string, any>;
  management_info: ManagementInfo;
}

export interface ApproveOvertimeRequestData {
  action: string;
  total_processed: number;
  total_failed: number;
  processed_requests: ProcessedRequest[];
  failed_requests?: FailedRequest[];
  group_code?: string;
  reject_reason?: string;
}

export interface ApproveOvertimeRequestResponse {
  success: boolean;
  message: string;
  data: ApproveOvertimeRequestData;
}

export interface ApproveOvertimeRequestPayload {
  id?: number;
  group_code?: string;
  action: Action;
  reject_reason?: string;
}

export interface ProcessedRequest {
  id: number;
  employee_name: string;
  additional_day: string;
  action: string;
  status: number;
}

export interface FailedRequest {
  id: number;
  reason: string;
}


// Bulk update requests interfaces (for leave requests)
export interface BulkUpdateRequestsPayload {
  id?: number;
  group_code?: string;
  action: Action;
  reject_reason?: string;
}

export interface BulkSuccessfulUpdate {
  id: number;
  staff_id: number;
  group_code: string | null;
  action: string;
  status: number;
  confirmation_date: string;
}

export interface BulkFailedUpdate {
  id: number;
  reason: string;
}

export interface BulkGroupSummary {
  group_code: string;
  action: string;
  processed_count: number;
  total_requests: number;
}

export interface BulkUpdateSummary {
  total_requests: number;
  successful_count: number;
  failed_count: number;
  action: string;
  confirmation_date: string;
  approver_id: number;
}

export interface BulkProcessingDetails {
  individual_requests_processed: number;
  group_requests_processed: number;
  unique_groups_affected: number;
}

export interface BulkUpdateRequestsResponse {
  success: boolean;
  message: string;
  summary: BulkUpdateSummary;
  successful_updates: BulkSuccessfulUpdate[];
  group_summaries?: BulkGroupSummary[];
  failed_updates?: BulkFailedUpdate[];
  reject_reason?: string;
  processing_details: BulkProcessingDetails;
}

export interface Pagination {
  page: number;
  total_pages: number;
  total_items: number;
  items_per_page?: number;
}

export interface PaginatedResponse<T = unknown> {
  status: boolean;
  message?: string;
  data?: T[];
  pagination?: Pagination;
  total?: number;
}

export interface OvertimeSummary{
  id:number
  total_mins:number
  total_requests:number
  total_pendings:number
  total_approves:number
}

export type OvertimeRequestResponse = ApiResponse<OvertimeRequest>;
export type WorkShiftResponse = ApiResponse<WorkShift[]>;
export type OvertimeResponse = PaginatedResponse<Overtime>;
export type OvertimeItemResponse = ApiResponse<Overtime>;
export type PaginatedOvertimeResponse = PaginatedResponse<Overtime>;
export type PaginatedAttendanceResponse = PaginatedResponse<Attendance>;
export type HolidayResponse = ApiResponse<Holiday[]>;
