
export type NotificationType = 'approve' | 'pending' | 'reject';

export interface  Notification {
  id: number;
  type: NotificationType;
  title: string;
  message: string;
  time: Date;
  read: boolean;
  sender?: {
    name: string;
    avatar?: string;
  };
  link?: string;
}

export interface ApiResponse<T = unknown> {
  status: boolean;
  message?: string;
  data?: T;
}



// Update overtimeresponse to support Pagination

