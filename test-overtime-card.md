# Test OvertimeCard Component

## Tính năng đã thêm

### 1. <PERSON><PERSON><PERSON> Approve/Reject

- Hi<PERSON><PERSON> thị nút "Phê duyệt" và "Từ chối" cho đơn tăng ca nhóm
- Chỉ hiển thị khi:
  - <PERSON><PERSON> đơn tăng ca nhóm (group overtime)
  - nhân viên nếu được đăng kí từ quản lí thì mới có thể nhấn phê duyệt/từ chối
  - Trạng thái đơn là "Pending"
  - Trạng thái của nhân viên hiện tại trong nhóm là "Pending"

### 2. Logic xử lý

- `handleApprove()`: Gọi API updateStatusOvertime để phê duyệt
- `handleReject()`: Gọi API updateStatusOvertime để từ chối
- Sử dụng mutation từ useAttendance composable
- Hiển thị toast message thành công/thất bại
- Emit events để parent component có thể handle

### 3. <PERSON><PERSON><PERSON> thị danh sách nhân viên

- <PERSON>ển thị danh sách nhân viên trong đơn tăng ca nhóm
- Hiển thị trạng thái của từng nhân viên (Pending/Approved/Rejected)
- Màu sắc khác nhau cho từng trạng thái

### 4. Translation keys đã thêm

```json
{
  "attendance": {
    "overtime": {
      "card": {
        "employees": "Nhân viên"
      },
      "actions": {
        "approve": "Phê duyệt",
        "reject": "Từ chối"
      },
      "messages": {
        "approved_success": "Phê duyệt đơn tăng ca thành công",
        "rejected_success": "Từ chối đơn tăng ca thành công",
        "approve_failed": "Phê duyệt đơn tăng ca thất bại",
        "reject_failed": "Từ chối đơn tăng ca thất bại"
      }
    }
  }
}
```

## Cách test

1. Đăng nhập với tài khoản là approver của một đơn tăng ca nhóm
2. Vào trang Overtime History
3. Tìm đơn tăng ca nhóm có trạng thái Pending
4. Kiểm tra xem có hiển thị nút "Phê duyệt" và "Từ chối" không
5. Click vào nút để test chức năng

## Files đã chỉnh sửa

1. `src/components/overtime/OvertimeCard.vue` - Component chính
2. `src/components/overtime/OvertimeList.vue` - Forward events
3. `src/views/OvertimeHistoryView.vue` - Handle events
4. `src/composables/useAttendance.ts` - Thêm mutation
5. `src/locales/*/attendance.json` - Thêm translations

## API được sử dụng

- `updateStatusOvertime(record_id, payload)` - Cập nhật trạng thái nhân viên trong đơn tăng ca
- Endpoint: `PUT /attendance/employee-status/{record_id}`
