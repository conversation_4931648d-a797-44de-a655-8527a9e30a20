import type {
  BulkUpdateRequestsPayload,
  BulkUpdateRequestsResponse,
} from '@/interfaces/attendance';
import type {
  LeaveMutationResponse,
  LeaveRequest,
} from '@/interfaces/leave';
import {
  bulkUpdateRequests,
  createLeaveRequest,
  getLeaveBalance,
  getLeaveRequest,
  getLeaveRequests,
  getManagementLeaveRequests,
  getRecentRequests,
  getStaffWithRemain,
  updateLeaveRequest,
} from '@/services/leaves.service';
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query';

import { PAGE_SIZE } from '@/constants';
import type { Ref } from 'vue';

export const useLeaveRequests = () => {
  const queryClient = useQueryClient();

  const listQuery = useQuery({
    queryKey: ['leave-requests'],
    queryFn: getLeaveRequests,
  });

  const leaveItemQuery = (id: number, enabled: boolean = true) => {
    return useQuery({
      queryKey: ['leave-request', id],
      queryFn: () => getLeaveRequest(id),
      enabled: enabled && !!id,
      select: (res) => res.data,
    });
  };

  const createMutation = useMutation<
    LeaveMutationResponse,
    Error,
    LeaveRequest
  >({
    mutationFn: createLeaveRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      queryClient.invalidateQueries({ queryKey: ['staff-remain'] });
      queryClient.invalidateQueries({ queryKey: ['leave-balances'] });
    },
  });

  const updateMutation = useMutation<
    LeaveMutationResponse,
    Error,
    { id: number; data: Partial<LeaveRequest> }
  >({
    mutationFn: ({ id, data }) => updateLeaveRequest(id, data),
    onSuccess: (response, variables) => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });

      queryClient.invalidateQueries({
        queryKey: ['leave-request', variables.id],
      });

      queryClient.invalidateQueries({ queryKey: ['staff-remain'] });
      queryClient.invalidateQueries({ queryKey: ['leave-balances'] });
    },
  });

  // Keep existing bulkUpdateMutation for compatibility
  const bulkUpdateMutation = useMutation<
    BulkUpdateRequestsResponse,
    Error,
    BulkUpdateRequestsPayload
  >({
    mutationFn: bulkUpdateRequests,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      queryClient.invalidateQueries({
        queryKey: ['management-leave-requests'],
      });
      queryClient.invalidateQueries({ queryKey: ['staff-remain'] });
      queryClient.invalidateQueries({ queryKey: ['leave-balances'] });
    },
  });
  // Add new mutation specifically for management approve/reject
  const approveLeaveRequestMutation = useMutation<
    BulkUpdateRequestsResponse, // Use BulkUpdateRequestsResponse instead
    Error,
    BulkUpdateRequestsPayload // Use BulkUpdateRequestsPayload instead
  >({
    mutationFn: bulkUpdateRequests, // Use bulkUpdateRequests instead of approveLeaveRequest
    onSuccess: () => {
      // Invalidate all leave related queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['leave-requests'] });
      queryClient.invalidateQueries({
        queryKey: ['management-leave-requests'],
      });
      queryClient.invalidateQueries({ queryKey: ['staff-remain'] });
      queryClient.invalidateQueries({ queryKey: ['leave-balances'] });
    },
  });

  const staffRemainQuery = useQuery({
    queryKey: ['staff-remain'],
    queryFn: getStaffWithRemain,
  });

  const leaveBalanceListQuery = useQuery({
    queryKey: ['leave-balances'],
    queryFn: getLeaveBalance,
  });

  const recentRequestQuery = (limit: number = PAGE_SIZE) => {
    return useQuery({
      queryKey: ['recent-leave-requests', limit],
      queryFn: () => getRecentRequests(limit),
      select: (data) => data.data,
    });
  };

  const managementLeaveQuery = (filters: Ref<Record<string, any>>) => {
    return useQuery({
      queryKey: ['management-leave-requests', filters],
      queryFn: () => getManagementLeaveRequests(filters.value),
    });
  };

  return {
    // Mutations
    createMutation,
    updateMutation,
    bulkUpdateMutation,
    approveLeaveRequestMutation, // This now uses the bulk update API
    // Queries
    listQuery,
    leaveItemQuery,
    staffRemainQuery,
    leaveBalanceListQuery,
    recentRequestQuery,
    managementLeaveQuery,
  };
};
