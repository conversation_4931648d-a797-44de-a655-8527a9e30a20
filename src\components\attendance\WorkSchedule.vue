<script setup lang="ts">
import { ref, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useLocale } from '@/composables/useLocale'
import CalendarHeader from '@/components/ui/calendar/CalendarHeader.vue'
import CalendarGrid from '@/components/ui/calendar/CalendarGrid.vue'
import ShiftDrawer from '@/components/ui/calendar/ShiftDrawer.vue'
import type { RawShift, Shift, CalendarDay } from '@/interfaces/calendar'
import { shiftDefinitions, scheduleData } from '@/mocks/shift_data'
import { navigateToLocalizedRoute } from '@/utils/localized-navigation'
import { REGISTER_OVERTIME } from '@/constants/routes'
import { Button } from '../ui/button'
import ShiftTable from './ShiftTable.vue'

const { t } = useI18n()
const { locale } = useLocale()

const currentDate = ref<Date>(new Date())
const showDrawer = ref<boolean>(false)
const selectedDay = ref<CalendarDay | null>(null)
const daysOfWeek = computed<string[]>(() => {
  return [
    t('attendance.days.sunday_short'),
    t('attendance.days.monday_short'),
    t('attendance.days.tuesday_short'),
    t('attendance.days.wednesday_short'),
    t('attendance.days.thursday_short'),
    t('attendance.days.friday_short'),
    t('attendance.days.saturday_short')
  ]
})

const calendarDays = computed<CalendarDay[]>(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()
  const firstDay = new Date(year, month, 1)
  const startDate = new Date(firstDay)
  startDate.setDate(startDate.getDate() - firstDay.getDay())
  const days: CalendarDay[] = []
  const today = new Date()

  for (let i = 0; i < 42; i++) {
    const date = new Date(startDate)
    date.setDate(startDate.getDate() + i)
    const dateString = date.toISOString().split('T')[0]
    const rawShifts: RawShift[] = scheduleData[dateString] || []
    const shifts: Shift[] = rawShifts.map((rawShift: RawShift) => ({
      ...rawShift,
      ...shiftDefinitions[rawShift.code]
    }))
    days.push({
      date: new Date(date),
      day: date.getDate(),
      is_current_month: date.getMonth() === month,
      is_today: date.toDateString() === today.toDateString(),
      has_shift: shifts.length > 0,
      shifts: shifts
    })
  }
  return days
})

const previousMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
}

const nextMonth = (): void => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
}

const selectDay = (dayData: CalendarDay): void => {
  if (dayData.is_current_month) {
    selectedDay.value = dayData
    showDrawer.value = true
  }
}

const closeDrawer = (): void => {
  showDrawer.value = false
  selectedDay.value = null
}

const formatDate = (date: Date): string => {
  return date.toLocaleDateString(locale.value, {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

const getShiftColor = (shiftCode: string): string => {
  const shiftType = shiftDefinitions[shiftCode]?.type
  switch (shiftType) {
    case 'Day': return 'bg-emerald-100 text-emerald-500 border border-emerald-500'
    case 'Night': return 'bg-blue-100 text-blue-500 border border-blue-500'
    case 'CM': return 'bg-purple-100 text-purple-500 border border-purple-500'
    case 'Admin': return 'bg-orange-100 text-orange-500 border border-orange-500'
    case 'Maternity': return 'bg-pink-100 text-pink-500 border border-pink-500'
    default: return 'bg-gray-500'
  }
}

const getShiftTypeStyle = (type: string): string => {
  switch (type) {
    case 'Day': return 'bg-green-100 text-green-800'
    case 'Night': return 'bg-blue-100 text-blue-800'
    case 'CM': return 'bg-purple-100 text-purple-800'
    case 'Admin': return 'bg-orange-100 text-orange-800'
    case 'Maternity': return 'bg-pink-100 text-pink-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const calculateDuration = (startTime: string, endTime: string): string => {
  const [startHour, startMin] = startTime.split(':').map(Number)
  const [endHour, endMin] = endTime.split(':').map(Number)
  const startMinutes = startHour * 60 + startMin
  let endMinutes = endHour * 60 + endMin
  if (endMinutes < startMinutes) {
    endMinutes += 24 * 60
  }
  const duration = endMinutes - startMinutes
  const hours = Math.floor(duration / 60)
  const minutes = duration % 60
  return `${hours}h${minutes > 0 ? ` ${minutes}m` : ''}`
}
</script>

<template>
  <h1 class="text-lg w-full mt-2 px-1 font-semibold">
    {{ t('attendance.work_schedule.title') }}
  </h1>
  <div class="py-1 px-1 w-full">
    <div class="bg-white rounded-lg shadow-sm p-3 mb-4">
      <CalendarHeader :current-date="currentDate" @previous-month="previousMonth" @next-month="nextMonth" />
    </div>
    <div class="bg-white rounded-lg shadow-sm p-4 xs:p-1.5">
      <CalendarGrid :days-of-week="daysOfWeek" :calendar-days="calendarDays" :get-shift-color="getShiftColor"
        @select-day="selectDay" />
    </div>
    <ShiftTable />
    <Button size="lg" class="w-full mt-4" @click="() => navigateToLocalizedRoute(REGISTER_OVERTIME, locale)">
      {{ t('attendance.register_overtime.title') }}
    </Button>
    <div v-if="showDrawer" class="fixed inset-0 bg-gray-500/20 bg-opacity-50 z-40" @click="closeDrawer"
      aria-hidden="true">
    </div>
    <ShiftDrawer :show-drawer="showDrawer" :selected-day="selectedDay" :format-date="formatDate"
      :get-shift-color="getShiftColor" :get-shift-type-style="getShiftTypeStyle" :calculate-duration="calculateDuration"
      @close-drawer="closeDrawer" />
  </div>
</template>
