<script setup lang="ts">
import LeaveItemDetail from '@/components/leaves/LeaveItemDetail.vue';
import { Button } from '@/components/ui/button';
import { Drawer, DrawerTrigger } from '@/components/ui/drawer';
import { Separator } from '@/components/ui/separator';
import { useLocale } from '@/composables/useLocale';
import {
  // formatLeaveType,
  formatRelType,
  // getLeaveTypeClass,
  // getLeaveTypeIcon,
  getStatusClass,
  getStatusIcon,
  getStatusText,
} from '@/constants/leave-history';
import type { Leave } from '@/interfaces/leave';
import { Calendar, Clock, Paperclip } from 'lucide-vue-next';
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';

interface Props {
  item: Leave;
}
const props = defineProps<Props>();

// const groupMemberLength = computed(() => {
//   return props.item.group_members?.length ?? 0;
// });

const { t } = useI18n();
const { locale } = useLocale();

const isDrawerOpen = ref(false);

const formatDateRange = (startDate: string, endDate: string) => {
  const start = new Date(startDate).toLocaleDateString(locale.value, {
    month: 'short',
    day: 'numeric',
  });

  if (startDate === endDate) {
    return start;
  }

  const end = new Date(endDate).toLocaleDateString(locale.value, {
    month: 'short',
    day: 'numeric',
  });

  return `${start} - ${end}`;
};

// Handler to close drawer
const handleCloseDrawer = () => {
  isDrawerOpen.value = false;
};
</script>

<template>
  <Drawer v-model:open="isDrawerOpen">
    <div class="rounded-lg border bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
      <!-- Leave Header -->
      <div class="mb-3 flex items-start justify-between">
        <div class="flex-1">
          <h3 class="font-medium text-gray-900">{{ props.item.subject }}</h3>
          <div class="mt-1 flex items-center gap-1">
            <div>{{ formatRelType(props.item.rel_type || "") }}</div>
            <!-- <div class="flex items-center gap-1 rounded-sm px-2 py-1 text-xs font-medium"
              :class="getLeaveTypeClass(props.item.type_of_leave)">
              <component :is="getLeaveTypeIcon(props.item.type_of_leave)" class="h-3 w-3" />
              {{ formatLeaveType(props.item.type_of_leave) }}
            </div>
            <div v-if="groupMemberLength > 0"
              class="flex items-center gap-1 rounded-sm border border-gray-400 bg-gray-500/10 px-2 py-1 text-xs font-medium text-gray-500">
              <Users class="h-3 w-3" stroke-width="2.5" />
              {{ t('leaves.card.team_leave') }} ({{ groupMemberLength }})
            </div> -->
          </div>
        </div>
        <div class="flex items-center gap-1 rounded-full px-2 py-1 text-xs font-medium"
          :class="getStatusClass(props.item.status)">
          <component :is="getStatusIcon(props.item.status)" class="h-3 w-3" />
          {{ getStatusText(props.item.status) }}
        </div>
      </div>

      <!-- Leave Details -->
      <div class="space-y-2">
        <div class="flex items-center gap-2 text-sm text-gray-600">
          <Calendar class="h-4 w-4" />
          <span>{{
            formatDateRange(props.item.start_time, props.item.end_time)
            }}</span>
        </div>

        <div v-if="props.item.number_of_leaving_day" class="flex items-center gap-2 text-sm text-gray-600">
          <Clock class="h-4 w-4" />
          <span>
            {{
              props.item.number_of_leaving_day > 1
                ? t('leaves.common.count_days', {
                  count: props.item.number_of_leaving_day,
                })
                : t('leaves.common.count_day', {
                  count: props.item.number_of_leaving_day,
                })
            }}
          </span>
        </div>

        <div v-if="props.item.reason" class="text-sm text-gray-600">
          <p class="line-clamp-2">{{ props.item.reason }}</p>
        </div>
        <!-- <div v-if="isRejected && props.item."
          class="rounded-md bg-red-50 p-2 text-sm text-red-600 border border-red-200">
          <strong>{{ t('leaves.card.rejected_reason') }}:</strong>
          <span>{{ props.item.rejected_reason }}</span>
        </div> -->
        <div v-if="props.item.attachment" class="flex items-center gap-2 text-sm text-blue-600">
          <Paperclip class="h-4 w-4" />
          <span>{{ t('leaves.detail.attachment_file') }}</span>
        </div>
      </div>

      <Separator class="my-4 bg-gray-300" />

      <!-- Action Buttons -->
      <div class="flex gap-2">
        <DrawerTrigger as-child>
          <Button variant="outline" size="lg" class="flex-1">
            {{ t('leaves.card.view_details') }}
          </Button>
        </DrawerTrigger>
        <!-- <Button
          variant="outline-destructive"
          size="lg"
          v-if="Number(props.item.status) === Status.PENDING"
        >
          {{ t('leaves.card.cancel') }}
        </Button> -->
      </div>
    </div>

    <!-- Leave Item Detail with close handler -->
    <LeaveItemDetail :item="item" @close="handleCloseDrawer" />
  </Drawer>
</template>
