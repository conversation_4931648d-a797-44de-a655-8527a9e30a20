<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import type { Notification } from '@/interfaces/common';
import { formatTimeAgo } from '@vueuse/core';
import { X } from 'lucide-vue-next';

const props = defineProps<{
  notification: Notification;
}>();

const emit = defineEmits<{
  (e: 'markAsRead', id: string): void;
  (e: 'remove', id: string): void;
}>();

const notificationColors = {
  approve: {
    border: '#28C76F',
    bg: 'bg-emerald-50',
  },
  pending: {
    border: '#FDEB71',
    bg: 'bg-amber-50',
  },
  reject: {
    border: '#F2383F',
    bg: 'bg-destructive/20',
  },
};

const handleMarkAsRead = () => {
  if (!props.notification.read) {
    emit('markAsRead', props.notification.id.toString());
  }
};

const handleRemove = (event: Event) => {
  event.stopPropagation();
  emit('remove', props.notification.id.toString());
};

const navigateToLink = () => {
  if (props.notification.link) {
    if (!props.notification.read) {
      emit('markAsRead', props.notification.id.toString());
    }
  } else {
    handleMarkAsRead();
  }
};
</script>

<template>
  <div
    class="mb-2 flex cursor-pointer rounded-lg p-4 shadow-sm transition-all duration-200 hover:-translate-y-0.5 hover:shadow-md"
    :class="[
      !notification.read
        ? notificationColors[notification.type].bg
        : 'bg-white',
      'border-l-4',
    ]" :style="{ borderLeftColor: notificationColors[notification.type].border }" @click="navigateToLink">
    <div class="flex-1">
      <div class="mb-1 flex items-center justify-between">
        <div class="flex items-center">
          <h3 class="text-base font-semibold text-gray-900">
            {{ notification.title }}
          </h3>
          <!-- <span v-if="!notification.read" class="bg-primary ml-2 h-2 w-2 rounded-full"></span> -->
        </div>
        <div class="flex items-center space-x-2">
          <button class="hover:text-destructive hover:bg-destructive/20 cursor-pointer rounded p-1 text-gray-500"
            @click.stop="handleRemove" title="Remove notification">
            <X class="size-4" />
          </button>
        </div>
      </div>
      <p class="mb-2 text-sm text-gray-600">{{ notification.message }}</p>
      <div class="flex justify-between text-xs text-gray-500">
        <span v-if="notification.sender">{{ notification.sender.name }}</span>
        <span>{{ formatTimeAgo(notification.time) }}</span>
      </div>
    </div>
  </div>
</template>
