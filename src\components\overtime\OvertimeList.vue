<template>
  <div class="w-full space-y-3">
    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center py-8">
      <div class="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
    </div>

    <!-- Empty State -->
    <div v-else-if="!overtimeRequests?.length" class="py-8 text-center">
      <p class="text-gray-500">
        {{ emptyMessage }}
      </p>
      <!-- Clear filters button for filtered empty state -->
      <Button v-if="showClearFiltersButton" variant="outline" size="sm" @click="emit('clearFilters')" class="mt-3">
        {{ t('attendance.filters.clear_filters') }}
      </Button>
    </div>

    <!-- Overtime Cards List -->
    <template v-else>
      <OvertimeCard v-for="overtime in overtimeRequests" :key="overtime.id" :overtime="overtime"
        @approve="handleApprove" @reject="handleReject" />
    </template>
  </div>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button';
import type { Overtime } from '@/interfaces/attendance';
import { computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import OvertimeCard from './OvertimeCard.vue';

interface Props {
  overtimeRequests: Overtime[];
  loading?: boolean;
  hasActiveFilters?: boolean;
}

interface Emits {
  clearFilters: [];
  approve: [id: number];
  reject: [id: number];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  hasActiveFilters: false,
});

const emit = defineEmits<Emits>();
const { t } = useI18n();

const emptyMessage = computed(() => {
  if (props.hasActiveFilters) {
    return t('attendance.overtime.messages.no_results_with_filters');
  }
  return t('attendance.overtime.messages.no_requests');
});

const showClearFiltersButton = computed(() => {
  return !props.overtimeRequests?.length && props.hasActiveFilters;
});

const handleApprove = (id: number) => {
  emit('approve', id);
};

const handleReject = (id: number) => {
  emit('reject', id);
};

watch(
  () => props.overtimeRequests,
  (newVal) => {
    console.log('overtimeRequests changed:', newVal);
  },
  { immediate: true, deep: true }
);
</script>
