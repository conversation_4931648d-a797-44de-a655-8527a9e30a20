<script setup lang="ts">
import { useI18n } from 'vue-i18n';

interface Stats {
  total: number;
  approved: number;
  pending: number;
  rejected: number;
}

interface Props {
  stats: Stats;
}

defineProps<Props>();

const { t } = useI18n();
</script>

<template>
  <div class="grid w-full grid-cols-4 items-center gap-2.5">
    <div
      class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2"
    >
      <span class="text-c-secondary text-xl font-bold">{{ stats.total }}</span>
      <span class="text-xs text-gray-500">{{
        t('leaves.statistics.total')
      }}</span>
    </div>
    <div
      class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2"
    >
      <span class="text-xl font-bold text-emerald-500">{{
        stats.approved
      }}</span>
      <span class="text-xs text-gray-500">
        {{ t('leaves.statistics.approved') }}
      </span>
    </div>
    <div
      class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2"
    >
      <span class="text-xl font-bold text-amber-500">{{ stats.pending }}</span>
      <span class="text-xs text-gray-500">
        {{ t('leaves.statistics.pending') }}
      </span>
    </div>
    <div
      class="flex flex-col items-center gap-1 rounded-md border border-gray-200 px-2.5 py-2"
    >
      <span class="text-c-primary text-xl font-bold">{{ stats.rejected }}</span>
      <span class="text-xs text-gray-500">
        {{ t('leaves.statistics.rejected') }}
      </span>
    </div>
  </div>
</template>
