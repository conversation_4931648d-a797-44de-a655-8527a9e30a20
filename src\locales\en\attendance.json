{"attendance": {"title": "Worksheet", "work_schedule": {"title": "Work Schedule", "present": "Present", "absent": "Absent", "half_day": "Half Day", "on_leave": "On Leave", "details": {"time": "Time", "no_schedules": "No work scheduled for this day."}, "table_notes": "Notes Table", "view_all": "View All"}, "filters": {"filter": "Filter", "active": "Filtered", "status": "Status", "date_from": "From Date", "date_to": "To Date", "select_status": "Select status", "select_date_from": "Select from date", "select_date_to": "Select to date", "clear_filters": "Clear Filters", "creator": "Creator", "creator_placeholder": "Select creator", "approver": "Approver", "approver_placeholder": "Select approver"}, "filter": {"title": "Filter", "select_date": "Select Date", "from_date": "From Date", "to_date": "To Date", "clear_filter": "Clear Filter"}, "status": {"all": "All", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "type": {"on_time": "On time", "late": "Late", "early": "Early", "absent": "Absent"}, "details": {"title": "Shift Details", "no_shift_available": "No Shift Available", "time": "Time", "time_in": "Time In", "time_out": "Time Out", "total_time": "Total Time", "half_day": "Half Day", "on_leave": "On Leave", "work_from_home": "Work from Home", "late_arrival": "Late Arrival", "early_leave": "Early Leave", "all_shifts": "All Shifts", "desc_shifts": "This is a list of all defined shifts in the system."}, "recent": {"title": "Recent Overtime Requests", "no_requests": "No recent overtime requests found.", "history": "Attendance History", "view_all": "View All Overtime Requests"}, "overtime": {"title": "Overtime History", "register_title": "Register Overtime", "history": {"title": "Overtime History"}, "summary": {"total_requests": "Total Requests", "approved_requests": "Approved", "pending_requests": "Pending", "rejected_requests": "Rejected", "total_hours": "Total Hours"}, "card": {"group": "Group", "individual": "Individual", "overtime_id": "#{id}", "date": "Date", "time": "Time", "duration": "Duration", "creator": "Creator", "approver": "Approver", "reason": "Reason", "rejection_reason": "Rejection Reason", "hours_suffix": "h"}, "messages": {"no_requests": "No overtime requests found", "no_results_with_filters": "No overtime requests found with current filters", "failed_to_load": "Failed to load overtime requests", "loading": "Loading overtime requests..."}}, "register_overtime": {"title": "Register Overtime", "edit_title": "Edit Overtime", "update": "Update Overtime", "submit": "Submit Overtime", "additional_day": "Additional Day", "work_shift": "Work Shift", "time_in": "Time In", "time_out": "Time Out", "timekeeping_value": "Timekeeping Value", "reason": "Reason", "approver": "Approver", "additional_day_placeholder": "Select additional day", "work_shift_placeholder": "Select work shift", "time_in_placeholder": "Select start time", "time_out_placeholder": "Select end time", "timekeeping_value_placeholder": "Auto calculated hours", "reason_placeholder": "Enter reason for overtime", "approver_placeholder": "Select approver"}, "summary": {"working_days": "Working days", "late_days": "Late days", "quited_days": "Quit days"}, "common": {"close": "Close", "unknown": "Unknown"}, "message": {"no_data": "No data available.", "no_attendance_history": "No attendance history found."}, "validation": {"additional_day_required": "Additional day is required", "shift_required": "Work shift is required", "approver_required": "Approver is required", "reason_required": "Reason is required", "time_in_required": "Time in is required", "time_out_required": "Time out is required", "must_be_a_number": "Must be a number", "reason_must_be_at_least_n_characters": "Reason must be at least {n} characters"}, "history": {"title": "Attendance History", "no_history": "No attendance history found.", "view_all": "View All Attendance History", "loading": "Loading attendance history...", "failed_to_load": "Failed to load attendance history"}, "month": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "days": {"sunday_short": "S", "monday_short": "M", "tuesday_short": "T", "wednesday_short": "W", "thursday_short": "T", "friday_short": "F", "saturday_short": "S"}, "update": {"status": "Status", "staff_id": "Staff ID", "name": "Name"}, "modals": {"title": "Overtime Request Details", "creator": "Creator", "approver": "Approver", "date": "Date", "time": "Time", "duration": "Duration", "reason": "Reason"}}}