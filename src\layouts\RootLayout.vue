<script setup lang="ts">
import { useLocale } from '@/composables/useLocale';
import { NOTIFICATION, PROFILE } from '@/constants/routes';
import { tabsList } from '@/constants/tabs';
import { useLayoutStore } from '@/stores/title-layout';
import {
  getLocalizedPath,
  navigateToLocalizedRoute,
} from '@/utils/localized-navigation';
import {
  IonContent,
  IonHeader,
  IonIcon,
  IonLabel,
  IonPage,
  IonRouterOutlet,
  IonTabBar,
  IonTabButton,
  IonTabs,
  IonTitle,
  IonToolbar,
} from '@ionic/vue';
import { notificationsOutline } from 'ionicons/icons';
import { useI18n } from 'vue-i18n';
import { useRoute } from 'vue-router';

const route = useRoute();
const layoutTitle = useLayoutStore();
const { locale } = useLocale();
const { t } = useI18n();

// Get localized path for a tab
const getTabPath = (basePath: string) => {
  return getLocalizedPath(basePath);
};

// Check if a tab is active
const isTabActive = (basePath: string) => {
  const localizedPath = getLocalizedPath(basePath);
  return route.path === localizedPath;
};
</script>

<template>
  <ion-page>
    <ion-header class="custom-toolbar">
      <ion-toolbar class="px-4">
        <div class="flex items-center justify-between">
          <ion-title class="!font-semibold">
            {{ t(layoutTitle.pageTitle) }}
          </ion-title>
          <div class="flex items-center gap-4">
            <button @click="navigateToLocalizedRoute(NOTIFICATION, locale)" class="cursor-pointer">
              <ion-icon :icon="notificationsOutline" class="size-6 [--ionicon-stroke-width:38px]" />
            </button>
            <button @click="navigateToLocalizedRoute(PROFILE, locale)">
              <div
                class="flex size-8 cursor-pointer items-center justify-center rounded-full bg-gray-200 text-gray-400">
                T
              </div>
            </button>
          </div>
        </div>
      </ion-toolbar>
    </ion-header>

    <ion-content>
      <ion-tabs>
        <ion-router-outlet />
        <ion-tab-bar slot="bottom" class="shadow-[0_2px_10px_rgba(0,_0,_0,_0.2)]">
          <ion-tab-button v-for="tab in tabsList" :key="tab.basePath" :tab="tab.tab" :href="getTabPath(tab.basePath)"
            class="custom-tab" :class="{ 'tab-selected': isTabActive(tab.basePath) }">
            <ion-icon :icon="tab.icon" />
            <ion-label>{{ t(tab.label) }}</ion-label>
          </ion-tab-button>
        </ion-tab-bar>
      </ion-tabs>
    </ion-content>
  </ion-page>
</template>

<style scoped>
.custom-toolbar {
  --box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.custom-tab {
  position: relative;
}

.tab-selected {
  color: var(--color-c-primary);
}

.tab-selected::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2px;
  background: var(--color-c-primary);
}

:deep(.tab-selected ion-icon),
:deep(.tab-selected ion-label) {
  color: var(--color-c-primary) !important;
}
</style>
