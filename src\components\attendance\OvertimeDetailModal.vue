<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useLocale } from '@/composables/useLocale';
import { NModal, NTag, NButton, NSpace, NDivider } from 'naive-ui';
import { formatDate } from '@/utils/format';
import { getStatusText } from '@/constants/leave-history';
// import { navigateToLocalizedRoute } from '@/utils/localized-navigation';
// import { UPDATE_OVERTIME } from '@/constants/routes';
import type { Overtime } from '@/interfaces/attendance';

interface Props {
  show: boolean;
  overtime: Overtime;
}

interface Emits {
  (e: 'update:show', value: boolean): void;
  (e: 'close'): void;
  (e: 'edit', identifier: number | string): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

const { t } = useI18n();
const { locale } = useLocale();

const showModal = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// // Handle edit button click (same logic as OvertimeCard)
// const handleEdit = () => {
//   const canEdit = props.overtime.group_info?.can_edit || props.overtime.can_edit;
//   if (!canEdit) return;

//   const identifier = props.overtime.group_info?.group_code || props.overtime.id;

//   emit('edit', identifier);

//   navigateToLocalizedRoute(
//     UPDATE_OVERTIME.replace(':id', String(identifier)),
//     locale.value,
//   );
//   emit('close');
// };

const closeModal = () => {
  emit('close');
};

// Get status tag type
const getStatusTagType = (status: number) => {
  switch (status) {
    case 1: return 'success';
    case 2: return 'error';
    default: return 'warning';
  }
};
</script>

<template>
  <n-modal v-model:show="showModal" preset="card" style="width: 90%; max-width: 500px;"
    :title="t('attendance.modals.title')" :bordered="false" size="huge" role="dialog" aria-modal="true">

    <div v-if="overtime" class="space-y-4">
      <!-- Status and ID -->
      <div class="flex justify-between items-center">
        <div>
          <h3 class="text-lg font-semibold">
            {{ t('attendance.overtime.card.overtime_id', { id: overtime.id }) }}
          </h3>
          <p class="text-sm text-gray-500">
            {{ formatDate(overtime.additional_day, locale) }}
          </p>
        </div>
        <n-tag :type="getStatusTagType(overtime.status)" size="large">
          {{ getStatusText(overtime.status) }}
        </n-tag>
      </div>

      <n-divider />

      <!-- Details -->
      <div class="grid grid-cols-1 gap-4">
        <!-- Time -->
        <div class="grid grid-cols-2 gap-4">
          <div>
            <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.time_in') }}</label>
            <p class="text-sm text-gray-900">{{ overtime.time_in || 'N/A' }}</p>
          </div>
          <div>
            <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.time_out') }}</label>
            <p class="text-sm text-gray-900">{{ overtime.time_out || 'N/A' }}</p>
          </div>
        </div>

        <!-- Timekeeping Value -->
        <div>
          <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.timekeeping_value')
          }}</label>
          <p class="text-sm text-gray-900">{{ overtime.timekeeping_value || 0 }}</p>
        </div>

        <!-- Session -->
        <div v-if="(overtime as any).session">
          <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.session') }}</label>
          <p class="text-sm text-gray-900">
            {{ (overtime as any).session === 1 ? t('attendance.register_overtime.full_day') :
              t('attendance.register_overtime.morning') }}
          </p>
        </div>

        <!-- Reason -->
        <div>
          <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.reason') }}</label>
          <p class="text-sm text-gray-900">{{ overtime.reason || 'N/A' }}</p>
        </div>

        <!-- Approver -->
        <div v-if="overtime.approver_info">
          <label class="text-sm font-medium text-gray-700">{{ t('attendance.register_overtime.approver') }}</label>
          <p class="text-sm text-gray-900">{{ overtime.approver_info.full_name }}</p>
        </div>

        <!-- Creator -->
        <div v-if="overtime.creator_info">
          <label class="text-sm font-medium text-gray-700">{{ t('attendance.modals.creator') }}</label>
          <p class="text-sm text-gray-900">{{ overtime.creator_info.full_name }}</p>
        </div>
      </div>

      <!-- Action Buttons -->
      <n-divider />
      <n-space justify="end">
        <n-button @click="closeModal">
          {{ t('common.cancel') }}
        </n-button>
        <!-- <n-button v-if="overtime.can_edit || overtime.group_info?.can_edit" type="primary" @click="handleEdit">
          {{ t('common.edit') }}
        </n-button> -->
      </n-space>
    </div>
  </n-modal>
</template>
