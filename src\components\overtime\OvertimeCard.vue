<script setup lang="ts">
import { Button } from '@/components/ui/button';
import { useLocale } from '@/composables/useLocale';
import { getStatusClass, getStatusIcon } from '@/constants/leave-history';
import { UPDATE_OVERTIME } from '@/constants/routes';
import { Status } from '@/enums';
import type { Overtime } from '@/interfaces/attendance';
import { formatDate } from '@/utils/format';
import { navigateToLocalizedRoute } from '@/utils/localized-navigation';
import { PencilIcon, CheckIcon, XIcon } from 'lucide-vue-next';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAttendance } from '@/composables/useAttendance';
import { useUserStore } from '@/stores/user';
import { toast } from 'vue-sonner';

interface Props {
  overtime: Overtime;
}
const props = defineProps<Props>();
const emit = defineEmits<{
  edit: [id: number | string];
  approve: [id: number];
  reject: [id: number];
}>();

const displayStatus = computed(() => {
  return props.overtime.group_info?.status ?? props.overtime.status;
});

const { t } = useI18n();
const { locale } = useLocale();
const { user } = useUserStore();
const { updateStatusOvertimeMutation } = useAttendance();

const isGroup = computed(() => !!props.overtime.group_info);

const getStatusBadgeClass = (status: number) => {
  const statusMap = [Status.PENDING, Status.APPROVED, Status.REJECTED] as Record<number, Status>;
  const mappedStatus = statusMap[status] || Status.PENDING;
  return `inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${getStatusClass(mappedStatus)}`;
};

const getStatusTextDisplay = (status: number) => {
  const statusMap: Record<number, string> = {
    0: t('attendance.status.pending'),
    1: t('attendance.status.approved'),
    2: t('attendance.status.rejected'),
  };
  return statusMap[status] || t('attendance.common.unknown');
};

const getStatusIconComponent = (status: number) => {
  const statusMap: Record<number, Status> = {
    0: Status.PENDING,
    1: Status.APPROVED,
    2: Status.REJECTED,
  };
  const mappedStatus = statusMap[status] || Status.PENDING;
  return getStatusIcon(mappedStatus);
};

// Check if current user can approve/reject this group overtime
const canApproveReject = computed(() => {
  if (!isGroup.value || !props.overtime.group_info) return false;

  // Chỉ cần group đang pending và nhân viên (chính là user) cũng đang pending
  const isGroupPending = displayStatus.value === Status.PENDING;

  return isGroupPending;
});


// Find current user's employee record in the group
const currentUserEmployee = computed(() => {
  if (!isGroup.value || !props.overtime.employees || !user) return null;

  return props.overtime.employees.find(emp =>
    emp.employee_id === user.id
  );
});

const handleEdit = () => {
  const canEdit = props.overtime.group_info?.can_edit || props.overtime.can_edit;
  if (!canEdit) return;

  const identifier = props.overtime.group_info?.group_code || props.overtime.id;

  emit('edit', identifier);

  navigateToLocalizedRoute(
    UPDATE_OVERTIME.replace(':id', String(identifier)),
    locale.value,
  );
};

const handleApprove = async () => {
  if (!currentUserEmployee.value || !user) return;

  try {
    // Create payload with approved status
    const payload = {
      employees: [currentUserEmployee.value.employee_id],
      creator: props.overtime.group_info?.creator,
      additional_day: props.overtime.group_info?.additional_day || '',
      timekeeping_value: String(props.overtime.group_info?.timekeeping_value || ''),
      time_in: props.overtime.group_info?.time_in || '',
      time_out: props.overtime.group_info?.time_out || '',
      approver: props.overtime.group_info?.approver || 0,
      reason: props.overtime.group_info?.reason || ''
    };

    await updateStatusOvertimeMutation.mutateAsync({
      record_id: currentUserEmployee.value.id,
      data: payload
    });
    emit('approve', currentUserEmployee.value.id);
    toast.success(t('attendance.overtime.messages.approved_success'));
  } catch {
    toast.error(t('attendance.overtime.messages.approve_failed'));
  }
};

const handleReject = async () => {
  if (!currentUserEmployee.value || !user) return;

  try {
    // Create payload with rejected status
    const payload = {
      employees: [currentUserEmployee.value.employee_id],
      creator: props.overtime.group_info?.creator,
      additional_day: props.overtime.group_info?.additional_day || '',
      timekeeping_value: String(props.overtime.group_info?.timekeeping_value || ''),
      time_in: props.overtime.group_info?.time_in || '',
      time_out: props.overtime.group_info?.time_out || '',
      approver: props.overtime.group_info?.approver || 0,
      reason: props.overtime.group_info?.reason || ''
    };

    await updateStatusOvertimeMutation.mutateAsync({
      record_id: currentUserEmployee.value.id,
      data: payload
    });
    emit('reject', currentUserEmployee.value.id);
    toast.success(t('attendance.overtime.messages.rejected_success'));
  } catch {
    toast.error(t('attendance.overtime.messages.reject_failed'));
  }
};
</script>

<template>
  <div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm transition-shadow hover:shadow-md">
    <div class="mb-3 flex items-center justify-between">
      <h3 class="text-base font-semibold text-gray-900">
        {{ isGroup
          ? $t('attendance.overtime.card.group')
          : $t('attendance.overtime.card.individual') }}
      </h3>
      <span :class="getStatusBadgeClass(displayStatus)">
        <component :is="getStatusIconComponent(displayStatus)" class="mr-1 h-3 w-3" />
        {{ getStatusTextDisplay(displayStatus) }}
      </span>
    </div>

    <!-- Main Info Grid -->
    <div class="mb-3 grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.date') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{ formatDate(overtime.additional_day, locale) }}
        </span>
      </div>

      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.duration') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? overtime.group_info?.timekeeping_value
              : overtime.timekeeping_value
          }}{{ $t('attendance.overtime.card.hours_suffix') }}
        </span>
      </div>

      <div class="col-span-2 flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.time') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? `${overtime.group_info?.time_in} - ${overtime.group_info?.time_out}`
              : `${overtime.time_in} - ${overtime.time_out}`
          }}
        </span>
      </div>
    </div>

    <!-- Creator Info -->
    <div class="mb-2 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.creator') }}:
        </span>
        <div class="text-right">
          <span class="font-medium text-gray-900">
            {{
              (isGroup
                ? overtime.group_info?.creator_info?.full_name
                : overtime.creator_info?.full_name) || $t('attendance.common.unknown')
            }}
          </span>
          <span v-if="!isGroup && overtime.creator_info?.staff_identifi" class="ml-1 text-gray-400">
            ({{ overtime.creator_info.staff_identifi }})
          </span>
          <span v-else class="ml-1 text-gray-400">
            ({{ overtime.group_info?.creator_info.staff_identifi }})
          </span>
        </div>
      </div>
    </div>

    <!-- Approver Info -->
    <div class="mb-3 text-sm">
      <div class="flex items-center justify-between">
        <span class="text-gray-500">
          {{ $t('attendance.overtime.card.approver') }}:
        </span>
        <span class="font-medium text-gray-900">
          {{
            isGroup
              ? overtime.group_info?.approver_info?.full_name
              : overtime.approver_info?.full_name || $t('attendance.common.unknown')
          }}
        </span>
      </div>
    </div>

    <!-- Group Employees (only show for group overtime) -->
    <div v-if="isGroup && overtime.employees && overtime.employees.length > 0" class="mb-3">
      <span class="text-xs font-medium text-gray-700">
        {{ $t('attendance.overtime.card.employees') }} ({{ overtime.employees.length }}):
      </span>
      <div class="mt-2 space-y-1">
        <div v-for="employee in overtime.employees" :key="employee.id"
          class="flex items-center justify-between rounded bg-gray-50 px-2 py-1 text-xs">
          <span class="font-medium text-gray-900">{{ employee.employee_info.full_name }}</span>
          <span :class="{
            'text-yellow-600': employee.employee_status === Status.PENDING,
            'text-green-600': employee.employee_status === Status.APPROVED,
            'text-red-600': employee.employee_status === Status.REJECTED
          }">
            {{ employee.employee_status_text }}
          </span>
        </div>
      </div>
    </div>

    <!-- Reason -->
    <div class="mb-3 rounded-md bg-gray-50 p-2">
      <span class="text-xs font-medium text-gray-700">
        {{ $t('attendance.overtime.card.reason') }}:
      </span>
      <p class="mt-1 text-sm text-gray-600">{{ isGroup ? overtime.group_info?.reason : overtime.reason }}</p>
    </div>

    <!-- Rejection Reason -->
    <div v-if="overtime.status === Status.REJECTED" class="mb-3 rounded-md bg-red-50 p-2">
      <span class="text-xs font-medium text-red-700">
        {{ $t('attendance.overtime.card.rejection_reason') }}:
      </span>
      <p class="mt-1 text-sm text-red-600">{{ isGroup ? overtime.group_info?.reject_reason : overtime.reject_reason }}
      </p>
    </div>

    <!-- Approve/Reject Buttons (only show for group overtime if user is approver and status is pending) -->
    <div v-if="canApproveReject" class="border-t pt-3">
      <div class="flex gap-2">
        <Button variant="default" size="sm" class="flex-1" :disabled="updateStatusOvertimeMutation.isPending.value"
          :loading="updateStatusOvertimeMutation.isPending.value" @click="handleApprove">
          <CheckIcon class="mr-2 h-4 w-4" />
          {{ $t('attendance.overtime.actions.approve') }}
        </Button>
        <Button variant="destructive" size="sm" class="flex-1" :disabled="updateStatusOvertimeMutation.isPending.value"
          :loading="updateStatusOvertimeMutation.isPending.value" @click="handleReject">
          <XIcon class="mr-2 h-4 w-4" />
          {{ $t('attendance.overtime.actions.reject') }}
        </Button>
      </div>
    </div>

    <!-- Edit Button (only show if can_edit is true) -->
    <div v-if="overtime.can_edit || overtime.group_info?.can_edit" class="border-t pt-3">
      <Button variant="outline" size="sm" class="w-full" @click="handleEdit">
        <PencilIcon class="mr-2 h-4 w-4" />
        {{ $t('attendance.register_overtime.edit_title') }}
      </Button>
    </div>
  </div>
</template>
