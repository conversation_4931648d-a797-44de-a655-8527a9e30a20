<template>
  <div class="-mt-5 grid w-full grid-cols-2 gap-4 lg:grid-cols-4">
    <div class="rounded-lg bg-white p-4 shadow-sm">
      <div class="text-sm font-medium text-gray-500">
        {{ $t('attendance.overtime.summary.total_requests') }}
      </div>
      <div class="mt-1 text-xl font-bold text-gray-900">
        {{ totalRequests }}
      </div>
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <div class="text-sm font-medium text-gray-500">
        {{ $t('attendance.overtime.summary.approved_requests') }}
      </div>
      <div class="mt-1 text-xl font-bold text-emerald-600">
        {{ approvedRequests }}
      </div>
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <div class="text-sm font-medium text-gray-500">
        {{ $t('attendance.overtime.summary.pending_requests') }}
      </div>
      <div class="mt-1 text-xl font-bold text-amber-600">
        {{ pendingRequests }}
      </div>
    </div>

    <div class="rounded-lg bg-white p-4 shadow-sm">
      <div class="text-sm font-medium text-gray-500">
        {{ $t('attendance.overtime.summary.total_hours') }}
      </div>
      <div class="mt-1 text-xl font-bold text-blue-600">
        {{ totalHours }}{{ $t('attendance.overtime.card.hours_suffix') }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  totalRequests: number;
  approvedRequests: number;
  pendingRequests: number;
  totalHours: number;
}

defineProps<Props>();
</script>
