<!-- eslint-disable no-unused-vars -->
<script setup lang="ts">
import { Button } from '@/components/ui/button';
import type { Notification, NotificationType } from '@/interfaces/common';
import { groupByDate } from '@/utils/date-util';
import { Bell } from 'lucide-vue-next';
import { computed, ref } from 'vue';
import NotificationItem from './NotificationItem.vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const props = defineProps<{
  notifications: Notification[];
}>();

const emit = defineEmits<{
  (e: 'markAsRead', id: string): void;
  (e: 'remove', id: string): void;
  (e: 'markAllAsRead'): void;
  (e: 'clearAll'): void;
}>();

const selectedFilter = ref<NotificationType | 'all'>('all');

const filteredNotifications = computed(() => {
  if (selectedFilter.value === 'all') {
    return [...props.notifications].sort(
      (a, b) => new Date(b.time).getTime() - new Date(a.time).getTime(),
    );
  }

  return [...props.notifications]
    .filter((notification) => notification.type === selectedFilter.value)
    .sort((a, b) => new Date(b.time).getTime() - new Date(a.time).getTime());
});

const groupedNotifications = computed(() => {
  return groupByDate(filteredNotifications.value);
});

const unreadCount = computed(() => {
  return props.notifications.filter((n) => !n.read).length;
});

const handleMarkAsRead = (id: string) => {
  emit('markAsRead', id);
};

const handleRemove = (id: string) => {
  emit('remove', id);
};

const handleMarkAllAsRead = () => {
  emit('markAllAsRead');
};

const handleClearAll = () => {
  emit('clearAll');
};

</script>

<template>
  <div class="mb-6 flex flex-col items-start justify-between sm:flex-row sm:items-center">
    <div class="flex gap-2">
      <Button variant="outline" size="sm" @click="handleMarkAllAsRead" :disabled="unreadCount === 0">
        {{ t('notification.mask_all_as_read') }}
      </Button>
      <Button variant="outline-destructive" size="sm" @click="handleClearAll" :disabled="notifications.length === 0">
        {{ t('notification.clear_all') }}
      </Button>
    </div>
  </div>

  <!-- <div class="mb-6 flex flex-wrap gap-2">
    <Button size="sm" v-for="filter in filters" :key="filter.type"
      class="rounded-full px-3 py-1.5 text-sm transition-colors duration-200" :class="[
        selectedFilter === filter.type
          ? 'bg-primary text-white'
          : 'bg-white text-gray-600 shadow-sm hover:bg-gray-100',
      ]" @click="selectedFilter = filter.type as NotificationType | 'all'">
      {{ filter.label }}
    </Button>
  </div> -->

  <div v-if="filteredNotifications.length > 0">
    <div v-for="(notifications, group) in groupedNotifications" :key="group">
      <NotificationItem v-for="notification in notifications" :key="notification.id" :notification="notification"
        @mark-as-read="handleMarkAsRead" @remove="handleRemove" />
    </div>
  </div>

  <div v-else class="py-12 text-center">
    <div class="mb-4 text-gray-400">
      <Bell class="mx-auto size-16" />
    </div>
    <h3 class="mb-2 text-lg font-semibold text-gray-900">{{ t('notification.no_notification') }}</h3>
    <p class="text-gray-600">
      You don't have any
      {{ selectedFilter !== 'all' ? selectedFilter : '' }} notifications at the
      moment.
    </p>
  </div>
</template>
